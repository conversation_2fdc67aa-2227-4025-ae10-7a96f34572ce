import { db } from '../lib/prisma';

// 数据库配置数据
const dbConfigs = [
  {
    code: 'freePat',
    name: '医药专利',
    category: '药物研发',
    description: '医药专利信息及原文下载'
  },
  {
    code: 'deviceCNImported',
    name: '医疗器械模板',
    category: 'Regulation',
    description: '新建医疗器械数据库时的基础模板，包含完整的字段配置和表结构'
  },
  {
    code: 'deviceCNEvaluation',
    name: '中国大陆审评',
    category: 'Regulation',
    description: '医疗器械审评进度跟踪、审评结论查询'
  },
  {
    code: 'deviceHK',
    name: '中国香港上市',
    category: 'Regulation',
    description: '中国香港已上市的医疗器械信息'
  },
  {
    code: 'deviceUS',
    name: '美国上市',
    category: '全球器械',
    description: '美国FDA批准的医疗器械信息'
  },
  {
    code: 'deviceJP',
    name: '日本上市',
    category: '全球器械',
    description: '日本PMDA批准的医疗器械信息'
  },
  {
    code: 'deviceUK',
    name: '英国上市',
    category: '全球器械',
    description: '英国MHRA批准的医疗器械信息'
  },
  {
    code: 'subjectNewdrug',
    name: '全球获批新药',
    category: '专题数据',
    description: '全球范围内获得批准的新药信息'
  }
];

// 示例医疗器械数据
const sampleDevices = [
  // 中国大陆上市设备
  {
    productName: '超声喷砂牙周治疗仪',
    companyName: '桂林维润医疗科技有限公司',
    registrationNumber: '桂械注准20232170043',
    managementType: '第二类',
    approvalDate: new Date('2025-06-16'),
    validUntil: new Date('2030-06-15'),
    category: '国产',
    structureOrUse: '用于牙周病治疗中清除牙石、菌斑等',
    productionAddress: '桂林市七星区东环路东侧GX-G08一号楼3层和4层、二号楼1层',
    companyAddress: '桂林市高新区信息产业园D-07号',
    specifications: 'DQ-40、DQ-80、DQ-81',
    structure: '该产品由超声喷砂牙周治疗仪主机、手柄、工作头、喷砂材料贮藏盒等组成',
    scope: '用于牙周病治疗中清除牙石、菌斑等',
    storageConditions: '常温保存',
    accessories: '手柄、工作头、喷砂材料贮藏盒',
    otherContent: '无',
    notes: '无',
    classification: '6823-4',
    approvalDepartment: '广西壮族自治区药品监督管理局',
    changeHistory: '首次注册',
    isInnovative: false,
    isClinicalNeed: false,
    isChildrenSpecific: false,
    isRareDisease: false,
    database: 'deviceCNEvaluation'
  },
  {
    productName: '血管造影X射线系统',
    companyName: '西门子医疗器械有限公司',
    registrationNumber: '国械注进***********',
    managementType: '第三类',
    approvalDate: new Date('2025-06-15'),
    validUntil: new Date('2029-04-07'),
    category: '进口',
    structureOrUse: '用于心血管介入诊断和治疗',
    productionAddress: '德国埃尔朗根',
    companyAddress: '上海市浦东新区张江高科技园区',
    specifications: 'Artis icono、Artis pheno',
    structure: '由X射线发生装置、影像增强器、数字化图像处理系统等组成',
    scope: '用于心血管介入诊断和治疗',
    storageConditions: '室温存储',
    accessories: '专用导管、造影剂注射器',
    otherContent: '具备3D成像功能',
    notes: '需专业技术人员操作',
    classification: '6830-1',
    approvalDepartment: '国家药品监督管理局',
    changeHistory: '变更注册',
    isInnovative: true,
    isClinicalNeed: true,
    isChildrenSpecific: false,
    isRareDisease: false,
    database: 'deviceCNImported'
  },
  {
    productName: '心脏起搏器',
    companyName: '美敦力（上海）医疗器械有限公司',
    registrationNumber: '国械注进***********',
    managementType: '第三类',
    approvalDate: new Date('2025-06-14'),
    validUntil: new Date('2028-05-12'),
    category: '进口',
    structureOrUse: '用于治疗心律失常',
    productionAddress: '美国明尼苏达州',
    companyAddress: '上海市浦东新区世纪大道88号',
    specifications: 'Azure XT、Azure MR',
    structure: '由脉冲发生器、电极导线等组成',
    scope: '用于治疗心律失常，维持正常心率',
    storageConditions: '2-8℃冷藏',
    accessories: '程控器、植入工具',
    otherContent: '具备MRI兼容性',
    notes: '需要定期程控检查',
    classification: '6854-1',
    approvalDepartment: '国家药品监督管理局',
    changeHistory: '首次注册',
    isInnovative: true,
    isClinicalNeed: true,
    isChildrenSpecific: false,
    isRareDisease: false,
    database: 'deviceCNImported'
  },
  // 美国上市设备
  {
    productName: 'da Vinci Surgical System',
    companyName: 'Intuitive Surgical, Inc.',
    registrationNumber: 'K193414',
    managementType: 'Class II',
    approvalDate: new Date('2025-05-20'),
    validUntil: new Date('2030-05-19'),
    category: '进口',
    structureOrUse: 'Robot-assisted minimally invasive surgery',
    productionAddress: 'Sunnyvale, California, USA',
    companyAddress: '1020 Kifer Road, Sunnyvale, CA 94086',
    specifications: 'da Vinci Xi, da Vinci SP',
    structure: 'Surgeon console, patient cart, vision cart, EndoWrist instruments',
    scope: 'General laparoscopic surgery, thoracoscopic surgery, urologic surgery',
    storageConditions: 'Room temperature',
    accessories: 'Various EndoWrist instruments, sterile drapes',
    otherContent: '3D HD vision system, motion scaling technology',
    notes: 'Requires specialized training',
    classification: 'BCZ',
    approvalDepartment: 'FDA',
    changeHistory: 'Product enhancement',
    isInnovative: true,
    isClinicalNeed: true,
    isChildrenSpecific: false,
    isRareDisease: false,
    database: 'deviceUS'
  },
  // 中国器械审评数据
  {
    productName: '人工智能辅助诊断软件',
    companyName: '北京推想科技有限公司',
    registrationNumber: '正在审评中',
    managementType: '第三类',
    approvalDate: null,
    validUntil: null,
    category: '国产',
    structureOrUse: '用于医学影像的AI辅助诊断',
    productionAddress: '北京市海淀区中关村软件园',
    companyAddress: '北京市海淀区中关村大街1号',
    specifications: 'InferRead CT、InferRead DR',
    structure: '由AI算法软件、数据处理模块组成',
    scope: '辅助医生进行CT、DR影像诊断',
    storageConditions: '云端部署',
    accessories: '无',
    otherContent: '基于深度学习技术',
    notes: '正在进行临床试验',
    classification: '6870-1',
    approvalDepartment: '国家药品监督管理局',
    changeHistory: '首次申报',
    isInnovative: true,
    isClinicalNeed: true,
    isChildrenSpecific: false,
    isRareDisease: false,
    database: 'deviceCNEvaluation'
  }
];

async function main() {
  try {
    console.log('🌱 Starting database seed data insertion...');

    // Insert database configurations
    console.log('📋 Inserting database configurations...');
    const createdDbConfigs = await db.databaseConfig.createMany({
      data: dbConfigs,
      skipDuplicates: true, // Skip existing records
    });

    // Insert medical device data
    console.log('🏥 Inserting medical device data...');
    const createdDevices = await db.medicalDevice.createMany({
      data: [
        {
          productName: "超声喷砂牙周治疗仪",
          companyName: "桂林维润医疗科技有限公司",
          registrationNumber: "桂械注准20232170043",
          managementType: "第二类",
          approvalDate: new Date("2025-06-16T00:00:00.000Z"),
          validUntil: new Date("2030-06-15T00:00:00.000Z"),
          category: "国产",
          structureOrUse: "用于牙周病治疗中清除牙石、菌斑等",
          productionAddress: "桂林市七星区东环路东侧GX-G08一号楼3层和4层、二号楼1层",
          companyAddress: "桂林市高新区信息产业园D-07号",
          specifications: "DQ-40、DQ-80、DQ-81",
          structure: "该产品由超声喷砂牙周治疗仪主机、手柄、工作头、喷砂材料贮藏盒等组成",
          scope: "用于牙周病治疗中清除牙石、菌斑等",
          storageConditions: "常温保存",
          accessories: "手柄、工作头、喷砂材料贮藏盒",
          otherContent: "无",
          notes: "无",
          classification: "6823-4",
          approvalDepartment: "广西壮族自治区药品监督管理局",
          changeHistory: "首次注册",
          isInnovative: false,
          isClinicalNeed: false,
          isChildrenSpecific: false,
          isRareDisease: false,
          database: "deviceCNEvaluation",
          businessKey: "桂械注准20232170043_deviceCNEvaluation"
        },
        {
          productName: "血管造影X射线系统",
          companyName: "西门子医疗器械有限公司",
          registrationNumber: "国械注进***********",
          managementType: "第三类",
          approvalDate: new Date("2025-06-15T00:00:00.000Z"),
          validUntil: new Date("2029-04-07T00:00:00.000Z"),
          category: "进口",
          structureOrUse: "用于心血管介入诊断和治疗",
          productionAddress: "德国埃尔朗根",
          companyAddress: "上海市浦东新区张江高科技园区",
          specifications: "Artis icono、Artis pheno",
          structure: "由X射线发生装置、影像增强器、数字化图像处理系统等组成",
          scope: "用于心血管介入诊断和治疗",
          storageConditions: "室温存储",
          accessories: "专用导管、造影剂注射器",
          otherContent: "具备3D成像功能",
          notes: "需专业技术人员操作",
          classification: "6830-1",
          approvalDepartment: "国家药品监督管理局",
          changeHistory: "变更注册",
          isInnovative: true,
          isClinicalNeed: true,
          isChildrenSpecific: false,
          isRareDisease: false,
          database: "deviceCNImported",
          businessKey: "国械注进***********_deviceCNImported"
        },
        {
          productName: "心脏起搏器",
          companyName: "美敦力（上海）医疗器械有限公司",
          registrationNumber: "国械注进***********",
          managementType: "第三类",
          approvalDate: new Date("2025-06-14T00:00:00.000Z"),
          validUntil: new Date("2028-05-12T00:00:00.000Z"),
          category: "进口",
          structureOrUse: "用于治疗心律失常",
          productionAddress: "美国明尼苏达州",
          companyAddress: "上海市浦东新区世纪大道88号",
          specifications: "Azure XT、Azure MR",
          structure: "由脉冲发生器、电极导线等组成",
          scope: "用于治疗心律失常，维持正常心率",
          storageConditions: "2-8℃冷藏",
          accessories: "程控器、植入工具",
          otherContent: "具备MRI兼容性",
          notes: "需要定期程控检查",
          classification: "6854-1",
          approvalDepartment: "国家药品监督管理局",
          changeHistory: "首次注册",
          isInnovative: true,
          isClinicalNeed: true,
          isChildrenSpecific: false,
          isRareDisease: false,
          database: "deviceCNImported",
          businessKey: "国械注进***********_deviceCNImported"
        },
        {
          productName: "da Vinci Surgical System",
          companyName: "Intuitive Surgical, Inc.",
          registrationNumber: "K193414",
          managementType: "Class II",
          approvalDate: new Date("2025-05-20T00:00:00.000Z"),
          validUntil: new Date("2030-05-19T00:00:00.000Z"),
          category: "进口",
          structureOrUse: "Robot-assisted minimally invasive surgery",
          productionAddress: "Sunnyvale, California, USA",
          companyAddress: "1020 Kifer Road, Sunnyvale, CA 94086",
          specifications: "da Vinci Xi, da Vinci SP",
          structure: "Surgeon console, patient cart, vision cart, EndoWrist instruments",
          scope: "General laparoscopic surgery, thoracoscopic surgery, urologic surgery",
          storageConditions: "Room temperature",
          accessories: "Various EndoWrist instruments, sterile drapes",
          otherContent: "3D HD vision system, motion scaling technology",
          notes: "Requires specialized training",
          classification: "BCZ",
          approvalDepartment: "FDA",
          changeHistory: "Product enhancement",
          isInnovative: true,
          isClinicalNeed: true,
          isChildrenSpecific: false,
          isRareDisease: false,
          database: "deviceUS",
          businessKey: "K193414_deviceUS"
        },
        {
          productName: "人工智能辅助诊断软件",
          companyName: "北京推想科技有限公司",
          registrationNumber: "正在审评中",
          managementType: "第三类",
          approvalDate: null,
          validUntil: null,
          category: "国产",
          structureOrUse: "用于医学影像的AI辅助诊断",
          productionAddress: "北京市海淀区中关村软件园",
          companyAddress: "北京市海淀区中关村大街1号",
          specifications: "InferRead CT、InferRead DR",
          structure: "由AI算法软件、数据处理模块组成",
          scope: "辅助医生进行CT、DR影像诊断",
          storageConditions: "云端部署",
          accessories: "无",
          otherContent: "基于深度学习技术",
          notes: "正在进行临床试验",
          classification: "6870-1",
          approvalDepartment: "国家药品监督管理局",
          changeHistory: "首次申报",
          isInnovative: true,
          isClinicalNeed: true,
          isChildrenSpecific: false,
          isRareDisease: false,
          database: "deviceCNEvaluation",
          businessKey: "正在审评中_deviceCNEvaluation"
        }
      ],
      skipDuplicates: true
    });

    console.log('✅ Seed data insertion completed!');
    console.log(`📊 Inserted ${createdDbConfigs.count} database configurations`);
    console.log(`🔬 Inserted ${createdDevices.count} medical device records`);

  } catch (error) {
    console.error('❌ Seed data insertion failed:', error);
    process.exit(1);
  } finally {
    await db.$disconnect();
  }
}

main();
